#!/usr/bin/env python3
"""
Test script to verify that all the compatibility fixes are working correctly.
"""

def test_numpy_compatibility():
    """Test NumPy compatibility with bottleneck and pyqtgraph"""
    try:
        import numpy
        print(f"✅ NumPy {numpy.__version__} imported successfully")
        
        import bottleneck
        print("✅ Bottleneck imported successfully")
        
        import pyqtgraph
        print("✅ PyQtGraph imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ NumPy compatibility test failed: {e}")
        return False

def test_pyqt_compatibility():
    """Test PyQt5 socket error compatibility"""
    try:
        from PyQt5.QtNetwork import QAbstractSocket, QLocalSocket
        
        # Test that both error types exist
        socket_error = QAbstractSocket.SocketError
        local_socket_error = QLocalSocket.LocalSocketError
        
        print(f"✅ QAbstractSocket.SocketError: {socket_error}")
        print(f"✅ QLocalSocket.LocalSocketError: {local_socket_error}")
        
        return True
    except Exception as e:
        print(f"❌ PyQt compatibility test failed: {e}")
        return False

def test_signal_client_import():
    """Test that SignalClient can be imported without errors"""
    try:
        from signal_client import SignalClient
        print("✅ SignalClient imported successfully")
        return True
    except Exception as e:
        print(f"❌ SignalClient import test failed: {e}")
        return False

def test_trending_window_import():
    """Test that trending_window can be imported without errors"""
    try:
        import trending_window
        print("✅ trending_window imported successfully")
        return True
    except Exception as e:
        print(f"❌ trending_window import test failed: {e}")
        return False

def main():
    """Run all compatibility tests"""
    print("🔧 Running compatibility tests...\n")
    
    tests = [
        ("NumPy Compatibility", test_numpy_compatibility),
        ("PyQt5 Compatibility", test_pyqt_compatibility),
        ("SignalClient Import", test_signal_client_import),
        ("TrendingWindow Import", test_trending_window_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        result = test_func()
        results.append(result)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All compatibility issues have been fixed!")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
