"""
独立策略信号监控进程 - 基于QLocalSocket/QLocalServer
接收来自trending_window和holdings_panel的策略信号，进行聚合处理并执行实盘交易
"""
import logging
import time
import json
import os
import sys
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QTextEdit, QPushButton, QTableWidget, QTableWidgetItem, QHeaderView
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot, QThread
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtNetwork import QLocalServer, QLocalSocket

# 导入配置和API服务
from config import PORTFOLIO_CONFIG
from api_service import APIService

logger = logging.getLogger(__name__)


@dataclass
class TradingSignal:
    """交易信号数据结构"""
    token_address: str
    symbol: str
    signal_type: str  # buy, sell, hold, wait
    price: float
    timestamp: int
    strategy_name: str
    source: str  # trending, holdings
    confidence: float = 0.0
    metadata: Dict = None
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = asdict(self)
        if data['metadata'] is None:
            data['metadata'] = {}
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TradingSignal':
        """从字典创建信号对象"""
        return cls(
            token_address=data['token_address'],
            symbol=data['symbol'],
            signal_type=data['signal_type'],
            price=data['price'],
            timestamp=data['timestamp'],
            strategy_name=data['strategy_name'],
            source=data['source'],
            confidence=data.get('confidence', 0.0),
            metadata=data.get('metadata', {})
        )


class TradingExecutor:
    """实盘交易执行器"""
    
    def __init__(self):
        self.api_service = APIService()
        self.last_execution_times = {}  # token_address -> timestamp
        self.execution_history = []  # 执行历史记录
        self.min_execution_interval = 60  # 最小执行间隔（秒）
        
        logger.info("TradingExecutor: 交易执行器初始化完成")
    
    def can_execute_signal(self, signal: TradingSignal) -> bool:
        """检查信号是否可以执行"""
        try:
            # 检查是否有足够的时间间隔
            last_time = self.last_execution_times.get(signal.token_address, 0)
            current_time = signal.timestamp
            
            if current_time <= last_time:
                logger.debug(f"TradingExecutor: 信号时间过旧，忽略 - {signal.symbol} ({current_time} <= {last_time})")
                return False
            
            if current_time - last_time < self.min_execution_interval:
                logger.debug(f"TradingExecutor: 执行间隔过短，忽略 - {signal.symbol} (间隔: {current_time - last_time}秒)")
                return False
            
            # 只执行买入和卖出信号，忽略持有和观察
            if signal.signal_type not in ['buy', 'sell']:
                logger.debug(f"TradingExecutor: 非交易信号，忽略 - {signal.symbol} ({signal.signal_type})")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"TradingExecutor: 检查信号执行条件失败: {e}")
            return False
    
    def execute_signal(self, signal: TradingSignal) -> bool:
        """执行交易信号"""
        try:
            if not self.can_execute_signal(signal):
                return False
            
            logger.info(f"TradingExecutor: 准备执行交易信号 - {signal.symbol} {signal.signal_type} @ ${signal.price:.6f}")
            
            # 模拟交易执行（在实际环境中，这里会调用真实的交易API）
            success = self._simulate_trade_execution(signal)
            
            if success:
                # 记录执行时间
                self.last_execution_times[signal.token_address] = signal.timestamp
                
                # 添加到执行历史
                execution_record = {
                    'timestamp': signal.timestamp,
                    'symbol': signal.symbol,
                    'signal_type': signal.signal_type,
                    'price': signal.price,
                    'source': signal.source,
                    'strategy_name': signal.strategy_name,
                    'executed_at': int(time.time())
                }
                self.execution_history.append(execution_record)
                
                # 保持历史记录大小
                if len(self.execution_history) > 1000:
                    self.execution_history = self.execution_history[-500:]
                
                logger.info(f"TradingExecutor: 交易执行成功 - {signal.symbol} {signal.signal_type}")
                return True
            else:
                logger.error(f"TradingExecutor: 交易执行失败 - {signal.symbol} {signal.signal_type}")
                return False
                
        except Exception as e:
            logger.error(f"TradingExecutor: 执行交易信号异常: {e}")
            return False
    
    def _simulate_trade_execution(self, signal: TradingSignal) -> bool:
        """模拟交易执行（在实际环境中替换为真实交易逻辑）"""
        try:
            # 🔥 模拟交易延迟
            time.sleep(0.1)
            
            # 🔥 模拟成功率（实际环境中根据市场条件和账户状态判断）
            import random
            success_rate = 0.85  # 85%成功率
            return random.random() < success_rate
            
        except Exception as e:
            logger.error(f"TradingExecutor: 模拟交易执行失败: {e}")
            return False
    
    def get_execution_statistics(self) -> Dict:
        """获取执行统计信息"""
        try:
            total_executions = len(self.execution_history)
            if total_executions == 0:
                return {
                    'total_executions': 0,
                    'buy_count': 0,
                    'sell_count': 0,
                    'unique_tokens': 0,
                    'recent_executions': []
                }
            
            buy_count = sum(1 for record in self.execution_history if record['signal_type'] == 'buy')
            sell_count = sum(1 for record in self.execution_history if record['signal_type'] == 'sell')
            unique_tokens = len(set(record['symbol'] for record in self.execution_history))
            recent_executions = self.execution_history[-10:]  # 最近10次执行
            
            return {
                'total_executions': total_executions,
                'buy_count': buy_count,
                'sell_count': sell_count,
                'unique_tokens': unique_tokens,
                'recent_executions': recent_executions
            }
            
        except Exception as e:
            logger.error(f"TradingExecutor: 获取执行统计失败: {e}")
            return {}


class SignalAggregator:
    """信号聚合器"""
    
    def __init__(self, trading_executor: TradingExecutor):
        self.trading_executor = trading_executor
        self.signal_buffer = []  # 信号缓冲区
        self.processed_signals = set()  # 已处理信号的唯一标识
        self.max_buffer_size = 1000
        
        # 聚合定时器
        self.aggregation_timer = QTimer()
        self.aggregation_timer.timeout.connect(self.process_signal_buffer)
        self.aggregation_timer.start(5000)  # 每5秒处理一次信号缓冲区
        
        logger.info("SignalAggregator: 信号聚合器初始化完成")
    
    def add_signal(self, signal: TradingSignal):
        """添加新信号到缓冲区"""
        try:
            # 生成信号唯一标识
            signal_id = f"{signal.token_address}_{signal.timestamp}_{signal.signal_type}"
            
            # 检查是否已处理过相同信号
            if signal_id in self.processed_signals:
                logger.debug(f"SignalAggregator: 重复信号，忽略 - {signal.symbol}")
                return
            
            # 添加到缓冲区
            self.signal_buffer.append(signal)
            self.processed_signals.add(signal_id)
            
            # 清理旧的处理记录
            if len(self.processed_signals) > 10000:
                # 保留最近的5000个记录
                recent_signals = sorted(self.signal_buffer, key=lambda x: x.timestamp)[-5000:]
                recent_ids = {f"{s.token_address}_{s.timestamp}_{s.signal_type}" for s in recent_signals}
                self.processed_signals = recent_ids
            
            # 限制缓冲区大小
            if len(self.signal_buffer) > self.max_buffer_size:
                self.signal_buffer = self.signal_buffer[-self.max_buffer_size//2:]
            
            logger.debug(f"SignalAggregator: 添加信号到缓冲区 - {signal.symbol} {signal.signal_type} (缓冲区: {len(self.signal_buffer)})")
            
        except Exception as e:
            logger.error(f"SignalAggregator: 添加信号失败: {e}")
    
    def process_signal_buffer(self):
        """处理信号缓冲区"""
        try:
            if not self.signal_buffer:
                return
            
            logger.debug(f"SignalAggregator: 开始处理信号缓冲区 ({len(self.signal_buffer)} 个信号)")
            
            # 按token分组
            signals_by_token = defaultdict(list)
            for signal in self.signal_buffer:
                signals_by_token[signal.token_address].append(signal)
            
            # 处理每个token的信号
            for token_address, signals in signals_by_token.items():
                if len(signals) == 1:
                    # 只有一个信号，直接执行
                    latest_signal = signals[0]
                else:
                    # 多个信号，选择最新的
                    latest_signal = max(signals, key=lambda x: x.timestamp)
                
                # 尝试执行信号
                executed = self.trading_executor.execute_signal(latest_signal)
                if executed:
                    logger.info(f"SignalAggregator: 执行了 {latest_signal.symbol} 的 {latest_signal.signal_type} 信号")
            
            # 清空缓冲区
            self.signal_buffer.clear()
            
        except Exception as e:
            logger.error(f"SignalAggregator: 处理信号缓冲区失败: {e}")


class SignalMonitorServer:
    """信号监控服务器"""
    
    # 信号定义
    signal_received = pyqtSignal(TradingSignal)
    client_connected = pyqtSignal(str)
    client_disconnected = pyqtSignal(str)
    
    def __init__(self, parent=None):
        self.parent = parent
        self.server = QLocalServer()
        self.clients = {}  # socket -> client_info
        self.server_name = "trading_signal_monitor"
        
        # 初始化交易执行器和信号聚合器
        self.trading_executor = TradingExecutor()
        self.signal_aggregator = SignalAggregator(self.trading_executor)
        
        # 连接信号
        self.server.newConnection.connect(self.on_new_connection)
        
        logger.info("SignalMonitorServer: 信号监控服务器初始化完成")
    
    def start_server(self) -> bool:
        """启动服务器"""
        try:
            # 清理可能存在的旧服务器
            QLocalServer.removeServer(self.server_name)
            
            # 启动新服务器
            if self.server.listen(self.server_name):
                logger.info(f"SignalMonitorServer: 服务器启动成功 - {self.server_name}")
                return True
            else:
                error_msg = self.server.errorString()
                logger.error(f"SignalMonitorServer: 服务器启动失败 - {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"SignalMonitorServer: 启动服务器异常: {e}")
            return False
    
    def stop_server(self):
        """停止服务器"""
        try:
            # 断开所有客户端
            for socket in list(self.clients.keys()):
                socket.disconnectFromServer()
                socket.deleteLater()
            
            self.clients.clear()
            
            # 关闭服务器
            self.server.close()
            QLocalServer.removeServer(self.server_name)
            
            logger.info("SignalMonitorServer: 服务器已停止")
            
        except Exception as e:
            logger.error(f"SignalMonitorServer: 停止服务器异常: {e}")
    
    def on_new_connection(self):
        """处理新客户端连接"""
        try:
            socket = self.server.nextPendingConnection()
            if socket:
                # 记录客户端信息
                client_info = {
                    'connected_at': time.time(),
                    'peer_name': 'Unknown',
                    'signal_count': 0
                }
                self.clients[socket] = client_info
                
                # 连接客户端信号
                socket.readyRead.connect(lambda: self.on_client_data_ready(socket))
                socket.disconnected.connect(lambda: self.on_client_disconnected(socket))
                
                logger.info(f"SignalMonitorServer: 新客户端连接 (总计: {len(self.clients)} 个)")
                
                if self.parent:
                    self.parent.client_connected.emit(f"客户端连接 (总计: {len(self.clients)} 个)")
                
        except Exception as e:
            logger.error(f"SignalMonitorServer: 处理新连接失败: {e}")
    
    def on_client_data_ready(self, socket: QLocalSocket):
        """处理客户端数据"""
        try:
            while socket.bytesAvailable():
                data = socket.readAll().data()
                message = data.decode('utf-8')

                # 🔥 修复：处理可能的多个JSON消息
                # 现在使用换行符分隔的消息格式
                messages = []

                # 按换行符分割消息
                lines = message.strip().split('\n')

                for line in lines:
                    line = line.strip()
                    if not line:  # 跳过空行
                        continue

                    try:
                        # 解析每一行作为独立的JSON消息
                        signal_data = json.loads(line)
                        messages.append(signal_data)
                    except json.JSONDecodeError as e:
                        logger.warning(f"SignalMonitorServer: JSON解析失败，跳过此行: {e}")
                        logger.debug(f"SignalMonitorServer: 问题行数据: {line[:100]}...")

                        # 如果换行符分割失败，尝试原来的方法作为备选
                        try:
                            decoder = json.JSONDecoder()
                            idx = 0
                            while idx < len(line):
                                try:
                                    obj, end_idx = decoder.raw_decode(line, idx)
                                    messages.append(obj)
                                    idx += end_idx
                                    # 跳过可能的空白字符
                                    while idx < len(line) and line[idx].isspace():
                                        idx += 1
                                except json.JSONDecodeError:
                                    break
                        except Exception:
                            pass  # 如果备选方法也失败，就跳过这行

                # 处理所有解析出的消息
                for signal_data in messages:
                    try:
                        signal = TradingSignal.from_dict(signal_data)

                        # 更新客户端信息
                        if socket in self.clients:
                            self.clients[socket]['signal_count'] += 1
                            self.clients[socket]['peer_name'] = signal.source

                        # 添加到信号聚合器
                        self.signal_aggregator.add_signal(signal)

                        logger.info(f"SignalMonitorServer: 接收到信号 - {signal.symbol} {signal.signal_type} (来源: {signal.source})")

                        # 通知UI
                        if self.parent:
                            self.parent.signal_received.emit(signal)

                    except Exception as signal_error:
                        logger.error(f"SignalMonitorServer: 处理单个信号失败: {signal_error}")
                        logger.debug(f"SignalMonitorServer: 问题信号数据: {signal_data}")

        except Exception as e:
            logger.error(f"SignalMonitorServer: 处理客户端数据失败: {e}")
            logger.debug(f"SignalMonitorServer: 原始消息: {message[:200] if 'message' in locals() else 'N/A'}...")
    
    def on_client_disconnected(self, socket: QLocalSocket):
        """处理客户端断开连接"""
        try:
            if socket in self.clients:
                client_info = self.clients[socket]
                logger.info(f"SignalMonitorServer: 客户端断开连接 - {client_info.get('peer_name', 'Unknown')} (信号数: {client_info.get('signal_count', 0)})")
                del self.clients[socket]
            
            socket.deleteLater()
            
            if self.parent:
                self.parent.client_disconnected.emit(f"客户端断开 (剩余: {len(self.clients)} 个)")
                
        except Exception as e:
            logger.error(f"SignalMonitorServer: 处理客户端断开失败: {e}")
    
    def get_server_statistics(self) -> Dict:
        """获取服务器统计信息"""
        try:
            total_signals = sum(client['signal_count'] for client in self.clients.values())
            
            return {
                'connected_clients': len(self.clients),
                'total_signals_received': total_signals,
                'execution_stats': self.trading_executor.get_execution_statistics(),
                'client_details': [
                    {
                        'name': info['peer_name'],
                        'connected_time': time.time() - info['connected_at'],
                        'signal_count': info['signal_count']
                    }
                    for info in self.clients.values()
                ]
            }
            
        except Exception as e:
            logger.error(f"SignalMonitorServer: 获取统计信息失败: {e}")
            return {}


class SignalMonitorWindow(QMainWindow):
    """信号监控主窗口"""
    
    # 信号定义
    signal_received = pyqtSignal(TradingSignal)
    client_connected = pyqtSignal(str)
    client_disconnected = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("策略信号监控中心")
        self.setGeometry(100, 100, 1000, 700)
        
        # 初始化服务器
        self.server = SignalMonitorServer(self)
        
        # 初始化UI
        self._init_ui()
        self._setup_timers()
        self._connect_signals()
        
        # 启动服务器
        if self.server.start_server():
            self.status_label.setText("状态: 服务器运行中 ✅")
        else:
            self.status_label.setText("状态: 服务器启动失败 ❌")
        
        logger.info("SignalMonitorWindow: 监控窗口初始化完成")
    
    def _init_ui(self):
        """初始化UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 设置深色主题
        self.setStyleSheet("""
            QMainWindow {
                background-color: #1e1e1e;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 12px;
            }
            QTextEdit {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #404040;
                font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
                font-size: 10px;
            }
            QTableWidget {
                background-color: #2d2d2d;
                color: #ffffff;
                border: 1px solid #404040;
                gridline-color: #404040;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #2d2d2d;
            }
            QPushButton {
                background-color: #61dafb;
                color: #1e1e1e;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4fa8c5;
            }
        """)
        
        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("状态: 初始化中...")
        self.status_label.setStyleSheet("font-weight: bold; color: #61dafb;")
        status_layout.addWidget(self.status_label)
        
        self.clients_label = QLabel("连接客户端: 0")
        status_layout.addWidget(self.clients_label)
        
        self.signals_label = QLabel("接收信号: 0")
        status_layout.addWidget(self.signals_label)
        
        self.executions_label = QLabel("执行交易: 0")
        status_layout.addWidget(self.executions_label)
        
        status_layout.addStretch()
        layout.addLayout(status_layout)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧：实时日志
        left_layout = QVBoxLayout()
        left_layout.addWidget(QLabel("实时信号日志:"))
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        left_layout.addWidget(self.log_text)
        
        # 执行历史表格
        left_layout.addWidget(QLabel("执行历史:"))
        self.execution_table = QTableWidget()
        self.execution_table.setColumnCount(6)
        self.execution_table.setHorizontalHeaderLabels(["时间", "代币", "信号", "价格", "来源", "策略"])
        self.execution_table.horizontalHeader().setStretchLastSection(True)
        left_layout.addWidget(self.execution_table)
        
        content_layout.addLayout(left_layout, 2)
        
        # 右侧：统计信息
        right_layout = QVBoxLayout()
        right_layout.addWidget(QLabel("服务器统计:"))
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(400)
        right_layout.addWidget(self.stats_text)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.restart_btn = QPushButton("重启服务器")
        self.restart_btn.clicked.connect(self.restart_server)
        button_layout.addWidget(self.restart_btn)
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_logs)
        button_layout.addWidget(self.clear_log_btn)
        
        right_layout.addLayout(button_layout)
        right_layout.addStretch()
        
        content_layout.addLayout(right_layout, 1)
        layout.addLayout(content_layout)
    
    def _setup_timers(self):
        """设置定时器"""
        # 统计信息更新定时器
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_statistics)
        self.stats_timer.start(10000)  # 每10秒更新一次
    
    def _connect_signals(self):
        """连接信号"""
        self.signal_received.connect(self.on_signal_received)
        self.client_connected.connect(self.on_client_status_change)
        self.client_disconnected.connect(self.on_client_status_change)
    
    @pyqtSlot(TradingSignal)
    def on_signal_received(self, signal: TradingSignal):
        """处理接收到的信号"""
        try:
            # 添加到日志
            timestamp_str = datetime.fromtimestamp(signal.timestamp).strftime("%H:%M:%S")
            log_entry = f"[{timestamp_str}] {signal.source.upper()}: {signal.symbol} {signal.signal_type.upper()} @ ${signal.price:.6f} ({signal.strategy_name})"
            
            self.log_text.append(log_entry)
            
            # 限制日志行数
            if self.log_text.document().blockCount() > 500:
                cursor = self.log_text.textCursor()
                cursor.movePosition(cursor.Start)
                cursor.movePosition(cursor.Down, cursor.KeepAnchor, 100)
                cursor.removeSelectedText()
            
        except Exception as e:
            logger.error(f"SignalMonitorWindow: 处理信号显示失败: {e}")
    
    @pyqtSlot(str)
    def on_client_status_change(self, message: str):
        """处理客户端状态变化"""
        self.clients_label.setText(f"连接客户端: {message}")
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            stats = self.server.get_server_statistics()
            
            # 更新标签
            self.clients_label.setText(f"连接客户端: {stats.get('connected_clients', 0)}")
            self.signals_label.setText(f"接收信号: {stats.get('total_signals_received', 0)}")
            
            exec_stats = stats.get('execution_stats', {})
            self.executions_label.setText(f"执行交易: {exec_stats.get('total_executions', 0)}")
            
            # 更新统计文本
            stats_text = f"""=== 服务器统计 ===
连接客户端: {stats.get('connected_clients', 0)}
总接收信号: {stats.get('total_signals_received', 0)}

=== 执行统计 ===
总执行次数: {exec_stats.get('total_executions', 0)}
买入信号: {exec_stats.get('buy_count', 0)}
卖出信号: {exec_stats.get('sell_count', 0)}
涉及代币: {exec_stats.get('unique_tokens', 0)}

=== 客户端详情 ===
"""
            
            for client in stats.get('client_details', []):
                stats_text += f"- {client['name']}: 连接{client['connected_time']:.0f}秒, {client['signal_count']}个信号\n"
            
            self.stats_text.setPlainText(stats_text)
            
            # 更新执行历史表格
            self.update_execution_table(exec_stats.get('recent_executions', []))
            
        except Exception as e:
            logger.error(f"SignalMonitorWindow: 更新统计信息失败: {e}")
    
    def update_execution_table(self, recent_executions: List[Dict]):
        """更新执行历史表格"""
        try:
            self.execution_table.setRowCount(len(recent_executions))
            
            for row, execution in enumerate(recent_executions):
                # 时间
                time_str = datetime.fromtimestamp(execution['timestamp']).strftime("%H:%M:%S")
                self.execution_table.setItem(row, 0, QTableWidgetItem(time_str))
                
                # 代币
                self.execution_table.setItem(row, 1, QTableWidgetItem(execution['symbol']))
                
                # 信号类型
                signal_item = QTableWidgetItem(execution['signal_type'].upper())
                if execution['signal_type'] == 'buy':
                    signal_item.setForeground(QColor("#4ade80"))
                elif execution['signal_type'] == 'sell':
                    signal_item.setForeground(QColor("#f87171"))
                self.execution_table.setItem(row, 2, signal_item)
                
                # 价格
                price_text = f"${execution['price']:.6f}" if execution['price'] < 1 else f"${execution['price']:.2f}"
                self.execution_table.setItem(row, 3, QTableWidgetItem(price_text))
                
                # 来源
                self.execution_table.setItem(row, 4, QTableWidgetItem(execution['source']))
                
                # 策略
                self.execution_table.setItem(row, 5, QTableWidgetItem(execution['strategy_name']))
                
        except Exception as e:
            logger.error(f"SignalMonitorWindow: 更新执行表格失败: {e}")
    
    def restart_server(self):
        """重启服务器"""
        try:
            self.status_label.setText("状态: 重启中...")
            
            # 停止服务器
            self.server.stop_server()
            
            # 重新启动
            if self.server.start_server():
                self.status_label.setText("状态: 服务器重启成功 ✅")
                logger.info("SignalMonitorWindow: 服务器重启成功")
            else:
                self.status_label.setText("状态: 服务器重启失败 ❌")
                logger.error("SignalMonitorWindow: 服务器重启失败")
                
        except Exception as e:
            logger.error(f"SignalMonitorWindow: 重启服务器失败: {e}")
            self.status_label.setText("状态: 重启异常 ❌")
    
    def clear_logs(self):
        """清空日志"""
        self.log_text.clear()
        self.execution_table.setRowCount(0)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        logger.info("SignalMonitorWindow: 正在关闭...")
        
        # 停止定时器
        if hasattr(self, 'stats_timer'):
            self.stats_timer.stop()
        
        # 停止服务器
        self.server.stop_server()
        
        super().closeEvent(event)


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    app = QApplication(sys.argv)
    
    # 创建监控窗口
    window = SignalMonitorWindow()
    window.show()
    
    logger.info("SignalMonitor: 应用程序启动")
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main() 