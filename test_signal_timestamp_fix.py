#!/usr/bin/env python3
"""
测试信号时间戳修复的脚本
验证信号发生的时间戳是否正确传递到信号监控器
"""

import logging
import time
import pandas as pd
from datetime import datetime
from multi_thread_ohlcv_manager import interpret_latest_signal_unified

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_signal_timestamp_extraction():
    """测试从DataFrame中提取信号时间戳的功能"""
    print("🔧 测试信号时间戳提取功能...\n")
    
    # 创建测试数据 - 模拟5分钟K线数据
    base_time = int(time.time()) - 3600  # 1小时前开始
    timestamps = [base_time + i * 300 for i in range(20)]  # 每5分钟一个数据点
    
    # 创建测试DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'open': [100 + i for i in range(20)],
        'high': [102 + i for i in range(20)],
        'low': [98 + i for i in range(20)],
        'close': [101 + i for i in range(20)],
        'volume': [1000] * 20,
        'signal': [0] * 20  # 初始化为0
    })
    
    # 在第10个位置设置买入信号
    df.loc[10, 'signal'] = 1
    # 在第15个位置设置卖出信号  
    df.loc[15, 'signal'] = -1
    
    print("📊 测试数据:")
    print(f"   - 数据点数量: {len(df)}")
    print(f"   - 时间范围: {datetime.fromtimestamp(timestamps[0]).strftime('%H:%M:%S')} - {datetime.fromtimestamp(timestamps[-1]).strftime('%H:%M:%S')}")
    print(f"   - 买入信号位置: 索引10, 时间戳: {timestamps[10]} ({datetime.fromtimestamp(timestamps[10]).strftime('%H:%M:%S')})")
    print(f"   - 卖出信号位置: 索引15, 时间戳: {timestamps[15]} ({datetime.fromtimestamp(timestamps[15]).strftime('%H:%M:%S')})")
    print()
    
    # 测试信号解释
    latest_signal, signal_index = interpret_latest_signal_unified(df, "TEST")
    
    print("🔍 信号解释结果:")
    print(f"   - 最终信号: {latest_signal}")
    print(f"   - 信号索引: {signal_index}")
    
    if signal_index >= 0 and signal_index < len(df):
        signal_timestamp = int(df['timestamp'].iloc[signal_index])
        signal_price = float(df['close'].iloc[signal_index])
        signal_time_str = datetime.fromtimestamp(signal_timestamp).strftime('%H:%M:%S')
        
        print(f"   - 信号时间戳: {signal_timestamp} ({signal_time_str})")
        print(f"   - 信号价格: ${signal_price:.2f}")
        
        # 验证时间戳是否正确
        current_time = int(time.time())
        time_diff = current_time - signal_timestamp
        
        print(f"   - 当前时间: {current_time} ({datetime.fromtimestamp(current_time).strftime('%H:%M:%S')})")
        print(f"   - 时间差: {time_diff}秒 ({time_diff/60:.1f}分钟)")
        
        if time_diff > 60:  # 如果时间差大于1分钟，说明使用的是历史时间戳
            print("   ✅ 正确：使用的是信号发生的历史时间戳，而不是当前时间")
            return True
        else:
            print("   ❌ 错误：可能使用的是当前时间而不是信号发生的时间")
            return False
    else:
        print("   ❌ 错误：无效的信号索引")
        return False

def test_different_signal_scenarios():
    """测试不同信号场景下的时间戳提取"""
    print("\n🔧 测试不同信号场景...\n")
    
    scenarios = [
        {
            'name': '只有买入信号',
            'signals': [0, 0, 0, 1, 0, 0, 0, 0, 0, 0],
            'expected_index': 3
        },
        {
            'name': '只有卖出信号',
            'signals': [0, 0, 0, 0, 0, -1, 0, 0, 0, 0],
            'expected_index': 5
        },
        {
            'name': '买入后持有',
            'signals': [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            'expected_index': 2
        },
        {
            'name': '买入后卖出',
            'signals': [0, 0, 1, 0, 0, 0, -1, 0, 0, 0],
            'expected_index': 6
        }
    ]
    
    all_passed = True
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. 测试场景: {scenario['name']}")
        
        # 创建测试数据
        base_time = int(time.time()) - 3600
        timestamps = [base_time + j * 300 for j in range(len(scenario['signals']))]
        
        df = pd.DataFrame({
            'timestamp': timestamps,
            'close': [100 + j for j in range(len(scenario['signals']))],
            'signal': scenario['signals']
        })
        
        # 解释信号
        latest_signal, signal_index = interpret_latest_signal_unified(df, f"TEST{i}")
        
        print(f"   - 信号: {latest_signal}, 索引: {signal_index}")
        
        if signal_index == scenario['expected_index']:
            signal_timestamp = int(df['timestamp'].iloc[signal_index])
            signal_time_str = datetime.fromtimestamp(signal_timestamp).strftime('%H:%M:%S')
            print(f"   - 时间戳: {signal_timestamp} ({signal_time_str})")
            print(f"   ✅ 索引正确")
        else:
            print(f"   ❌ 索引错误: 期望 {scenario['expected_index']}, 实际 {signal_index}")
            all_passed = False
        
        print()
    
    return all_passed

def main():
    """主函数"""
    print("🔧 信号时间戳修复测试\n")
    print("=" * 60)
    
    # 测试1: 基本时间戳提取
    test1_passed = test_signal_timestamp_extraction()
    
    # 测试2: 不同信号场景
    test2_passed = test_different_signal_scenarios()
    
    print("=" * 60)
    print("📋 测试总结:")
    print(f"   - 基本时间戳提取: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"   - 不同信号场景: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！信号时间戳修复成功！")
        print("\n📝 修复说明:")
        print("- 现在使用信号发生的实际时间戳，而不是消息接收时间")
        print("- 通过 interpret_latest_signal_unified 返回的索引获取准确时间戳")
        print("- 在 multi_thread_ohlcv_manager.py 中正确提取信号时间和价格")
        print("- 信号监控器将显示信号发生的真实时间")
        return True
    else:
        print("\n❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
