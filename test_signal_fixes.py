#!/usr/bin/env python3
"""
测试信号修复的综合脚本
验证时间戳修复和JSON解析修复是否有效
"""

import logging
import time
import json
from datetime import datetime
from signal_client import SignalData, create_signal_from_strategy_signal

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_signal_timestamp_logic():
    """测试信号时间戳逻辑"""
    print("🔧 测试信号时间戳逻辑...\n")
    
    # 创建一个历史时间戳（1小时前）
    historical_timestamp = int(time.time()) - 3600
    current_timestamp = int(time.time())
    
    # 创建测试信号
    signal = create_signal_from_strategy_signal(
        token_address="test_address",
        symbol="TEST",
        signal_type="buy",
        price=100.0,
        timestamp=historical_timestamp,  # 使用历史时间戳
        strategy_name="测试策略"
    )
    
    print("📊 测试信号数据:")
    print(f"   - 代币: {signal.symbol}")
    print(f"   - 信号类型: {signal.signal_type}")
    print(f"   - 价格: ${signal.price}")
    print(f"   - 信号时间戳: {signal.timestamp} ({datetime.fromtimestamp(signal.timestamp).strftime('%H:%M:%S')})")
    print(f"   - 当前时间戳: {current_timestamp} ({datetime.fromtimestamp(current_timestamp).strftime('%H:%M:%S')})")
    print(f"   - 时间差: {current_timestamp - signal.timestamp}秒 ({(current_timestamp - signal.timestamp)/60:.1f}分钟)")
    
    if signal.timestamp == historical_timestamp:
        print("   ✅ 正确：信号保持了历史时间戳")
        return True
    else:
        print("   ❌ 错误：信号时间戳被修改")
        return False

def test_json_serialization():
    """测试JSON序列化和分隔符"""
    print("\n🔧 测试JSON序列化和分隔符...\n")
    
    # 创建多个测试信号
    signals = []
    base_time = int(time.time()) - 1800  # 30分钟前
    
    for i in range(3):
        signal = create_signal_from_strategy_signal(
            token_address=f"test_address_{i}",
            symbol=f"TEST{i}",
            signal_type="buy" if i % 2 == 0 else "sell",
            price=100.0 + i * 10,
            timestamp=base_time + i * 300,  # 每5分钟一个信号
            strategy_name=f"测试策略{i}"
        )
        signals.append(signal)
    
    print("📊 测试信号序列化:")
    
    # 测试单个信号序列化
    for i, signal in enumerate(signals):
        try:
            # 模拟信号客户端的序列化逻辑
            message = json.dumps(signal.to_dict(), ensure_ascii=False)
            message_with_separator = message + '\n'
            
            print(f"   信号{i+1}: {signal.symbol} {signal.signal_type}")
            print(f"   - JSON长度: {len(message)}字符")
            print(f"   - 带分隔符长度: {len(message_with_separator)}字符")
            
            # 验证可以正确解析
            parsed = json.loads(message)
            reconstructed = SignalData.from_dict(parsed)
            
            if (reconstructed.symbol == signal.symbol and 
                reconstructed.signal_type == signal.signal_type and
                reconstructed.timestamp == signal.timestamp):
                print(f"   ✅ 序列化/反序列化成功")
            else:
                print(f"   ❌ 序列化/反序列化失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 信号{i+1}序列化失败: {e}")
            return False
    
    # 测试多个信号连接的情况
    print("\n   测试多信号连接解析:")
    try:
        # 模拟多个信号连在一起的情况
        combined_message = ""
        for signal in signals:
            message = json.dumps(signal.to_dict(), ensure_ascii=False)
            combined_message += message + '\n'
        
        print(f"   - 组合消息长度: {len(combined_message)}字符")
        print(f"   - 包含{len(signals)}个信号")
        
        # 模拟信号监控器的解析逻辑
        lines = combined_message.strip().split('\n')
        parsed_signals = []
        
        for line in lines:
            line = line.strip()
            if line:
                signal_data = json.loads(line)
                parsed_signals.append(SignalData.from_dict(signal_data))
        
        if len(parsed_signals) == len(signals):
            print(f"   ✅ 成功解析{len(parsed_signals)}个信号")
            
            # 验证每个信号的完整性
            all_correct = True
            for orig, parsed in zip(signals, parsed_signals):
                if (orig.symbol != parsed.symbol or 
                    orig.signal_type != parsed.signal_type or
                    orig.timestamp != parsed.timestamp):
                    all_correct = False
                    break
            
            if all_correct:
                print("   ✅ 所有信号数据完整正确")
                return True
            else:
                print("   ❌ 部分信号数据不正确")
                return False
        else:
            print(f"   ❌ 解析信号数量不匹配: 期望{len(signals)}, 实际{len(parsed_signals)}")
            return False
            
    except Exception as e:
        print(f"   ❌ 多信号解析失败: {e}")
        return False

def test_signal_data_integrity():
    """测试信号数据完整性"""
    print("\n🔧 测试信号数据完整性...\n")
    
    # 创建包含各种数据类型的信号
    test_cases = [
        {
            'name': '基本信号',
            'data': {
                'token_address': 'So11111111111111111111111111111111111111112',
                'symbol': 'SOL',
                'signal_type': 'buy',
                'price': 123.456789,
                'timestamp': int(time.time()) - 600,
                'strategy_name': 'VWAP交叉策略',
                'confidence': 0.85,
                'metadata': {'source': 'trending', 'timeframe': '5m'}
            }
        },
        {
            'name': '特殊字符信号',
            'data': {
                'token_address': 'test_address_with_special_chars',
                'symbol': 'TEST-币',
                'signal_type': 'sell',
                'price': 0.000123,
                'timestamp': int(time.time()) - 300,
                'strategy_name': '测试策略 (特殊字符)',
                'confidence': 0.92,
                'metadata': {'note': '包含中文和特殊字符: !@#$%^&*()'}
            }
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. 测试: {test_case['name']}")
        
        try:
            # 创建信号
            signal = SignalData(**test_case['data'])
            
            # 序列化
            signal_dict = signal.to_dict()
            json_str = json.dumps(signal_dict, ensure_ascii=False)
            
            # 反序列化
            parsed_dict = json.loads(json_str)
            reconstructed_signal = SignalData.from_dict(parsed_dict)
            
            # 验证数据完整性
            checks = [
                ('token_address', signal.token_address == reconstructed_signal.token_address),
                ('symbol', signal.symbol == reconstructed_signal.symbol),
                ('signal_type', signal.signal_type == reconstructed_signal.signal_type),
                ('price', abs(signal.price - reconstructed_signal.price) < 1e-10),
                ('timestamp', signal.timestamp == reconstructed_signal.timestamp),
                ('strategy_name', signal.strategy_name == reconstructed_signal.strategy_name),
                ('confidence', abs(signal.confidence - reconstructed_signal.confidence) < 1e-10),
                ('metadata', signal.metadata == reconstructed_signal.metadata)
            ]
            
            failed_checks = [name for name, passed in checks if not passed]
            
            if not failed_checks:
                print(f"   ✅ 数据完整性验证通过")
            else:
                print(f"   ❌ 数据完整性验证失败: {failed_checks}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            all_passed = False
        
        print()
    
    return all_passed

def main():
    """主函数"""
    print("🔧 信号修复综合测试\n")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("信号时间戳逻辑", test_signal_timestamp_logic),
        ("JSON序列化和分隔符", test_json_serialization),
        ("信号数据完整性", test_signal_data_integrity)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"测试 '{test_name}' 执行失败: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("=" * 60)
    print("📋 测试总结:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"   - {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 所有测试通过！信号修复成功！")
        print("\n📝 修复内容:")
        print("- ✅ 修复了字体问题：将 Consolas 替换为系统兼容字体")
        print("- ✅ 修复了JSON解析问题：改进多消息处理逻辑")
        print("- ✅ 修复了时间戳问题：使用信号发生的实际时间")
        print("- ✅ 改进了消息分隔：使用换行符分隔JSON消息")
        print("- ✅ 增强了错误处理：更好的异常处理和日志记录")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
